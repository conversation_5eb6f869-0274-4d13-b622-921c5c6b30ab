#!/usr/bin/env python3
# encoding: utf-8
"""
文件操作工具模块
提供常用的文件和目录操作功能
"""

import os
import shutil
import subprocess
from pathlib import Path
from typing import Optional, List
from utils.exceptions import ArtifactError
from utils.logger import get_logger

logger = get_logger(__name__)


def find_archive_file(
    search_dir: str,
    module_name: str,
    file_type: str,
    package_name: Optional[str] = None
) -> str:
    """
    查找构建产物文件
    
    Args:
        search_dir: 搜索目录
        module_name: 模块名称
        file_type: 文件类型 (jar, zip等)
        package_name: 包名称，默认使用模块名称
    
    Returns:
        找到的文件路径
        
    Raises:
        ArtifactError: 文件未找到或找到多个文件
    """
    if not package_name:
        package_name = module_name
    
    search_path = Path(search_dir)
    
    if file_type == "jar":
        # 在target目录中查找jar文件
        pattern = f"**/target/*{package_name}.{file_type}"
    else:
        # 直接查找文件
        pattern = f"**/{module_name}.{file_type}"
    
    found_files = list(search_path.glob(pattern))
    
    if not found_files:
        raise ArtifactError(f"未找到构建产物: {package_name}.{file_type}")
    
    if len(found_files) > 1:
        files_str = '\n'.join(str(f) for f in found_files)
        raise ArtifactError(f"找到多个构建产物文件:\n{files_str}")
    
    result = str(found_files[0])
    logger.info(f"找到构建产物: {result}")
    return result


def create_zip_archive(source_dir: str, output_file: str, exclude_patterns: List[str] = None) -> None:
    """
    创建ZIP压缩包
    
    Args:
        source_dir: 源目录
        output_file: 输出文件路径
        exclude_patterns: 排除的文件模式列表
    """
    try:
        source_path = Path(source_dir)
        output_path = Path(output_file)
        
        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用zip命令创建压缩包
        cmd = ["zip", "-qr", str(output_path), "."]
        
        # 添加排除模式
        if exclude_patterns:
            for pattern in exclude_patterns:
                cmd.extend(["-x", pattern])
        
        result = subprocess.run(
            cmd,
            cwd=source_path,
            check=True,
            capture_output=True,
            text=True
        )
        
        logger.info(f"成功创建压缩包: {output_file}")
        
    except subprocess.CalledProcessError as e:
        raise ArtifactError(f"创建压缩包失败: {e.stderr}")
    except Exception as e:
        raise ArtifactError(f"创建压缩包时发生错误: {e}")


def ensure_directory(path: str) -> None:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        path: 目录路径
    """
    Path(path).mkdir(parents=True, exist_ok=True)


def clean_directory(path: str, keep_hidden: bool = True) -> None:
    """
    清理目录内容
    
    Args:
        path: 目录路径
        keep_hidden: 是否保留隐藏文件
    """
    dir_path = Path(path)
    if not dir_path.exists():
        return
    
    for item in dir_path.iterdir():
        if keep_hidden and item.name.startswith('.'):
            continue
        
        try:
            if item.is_dir():
                shutil.rmtree(item)
            else:
                item.unlink()
        except Exception as e:
            logger.warning(f"清理文件失败 {item}: {e}")


def copy_file(src: str, dst: str, create_dirs: bool = True) -> None:
    """
    复制文件
    
    Args:
        src: 源文件路径
        dst: 目标文件路径
        create_dirs: 是否自动创建目标目录
    """
    src_path = Path(src)
    dst_path = Path(dst)
    
    if not src_path.exists():
        raise FileNotFoundError(f"源文件不存在: {src}")
    
    if create_dirs:
        dst_path.parent.mkdir(parents=True, exist_ok=True)
    
    shutil.copy2(src_path, dst_path)
    logger.debug(f"文件复制成功: {src} -> {dst}")


def get_file_size(file_path: str) -> int:
    """
    获取文件大小（字节）
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件大小
    """
    return Path(file_path).stat().st_size


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小显示
    
    Args:
        size_bytes: 文件大小（字节）
        
    Returns:
        格式化后的大小字符串
    """
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"
