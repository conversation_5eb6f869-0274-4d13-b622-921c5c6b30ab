#!/usr/bin/env python3
# encoding: utf-8
"""
Nexus制品仓库客户端
处理构建产物的上传和管理
"""

import requests
from pathlib import Path
from typing import Optional, Dict, Any
from config.settings import settings
from utils.exceptions import NexusError
from utils.file_utils import get_file_size, format_file_size
from utils.logger import get_logger

logger = get_logger(__name__)


class NexusClient:
    """Nexus制品仓库客户端"""
    
    def __init__(self):
        self.base_url = settings.nexus_base_url
        self.username = settings.NEXUS_USERNAME
        self.password = settings.NEXUS_PASSWORD
        self.push_enabled = settings.PUSH_NEXUS_ENABLE
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建HTTP会话"""
        session = requests.Session()
        
        if self.username and self.password:
            session.auth = (self.username, self.password)
        
        session.headers.update({
            'User-Agent': 'Luban-DevOps/2.0'
        })
        
        return session
    
    def upload_artifact(
        self,
        module_name: str,
        tag: str,
        file_path: str,
        artifact_type: str = "zip"
    ) -> bool:
        """
        上传构建产物到Nexus
        
        Args:
            module_name: 模块名称
            tag: 版本标签
            file_path: 文件路径
            artifact_type: 制品类型
            
        Returns:
            上传是否成功
        """
        if not self.push_enabled:
            logger.info(f"跳过上传制品: {module_name}")
            return False
        
        try:
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                raise NexusError(f"文件不存在: {file_path}")
            
            # 构建上传URL
            upload_url = self._build_upload_url(module_name, tag, artifact_type)
            
            # 获取文件信息
            file_size = get_file_size(file_path)
            logger.info(f"准备上传制品: {module_name} ({format_file_size(file_size)})")
            
            # 上传文件
            with open(file_path, 'rb') as file_obj:
                response = self.session.put(
                    upload_url,
                    data=file_obj,
                    timeout=1800  # 30分钟超时
                )
            
            if response.status_code == 201:
                logger.info(f"制品上传成功: {upload_url}")
                return True
            else:
                raise NexusError(f"上传失败 (HTTP {response.status_code}): {response.text}")
                
        except Exception as e:
            logger.error(f"上传制品失败 {module_name}: {e}")
            raise NexusError(f"制品上传失败: {e}")
    
    def _build_upload_url(self, module_name: str, tag: str, artifact_type: str) -> str:
        """
        构建上传URL
        
        Args:
            module_name: 模块名称
            tag: 版本标签
            artifact_type: 制品类型
            
        Returns:
            上传URL
        """
        return f"{self.base_url}/docker/plinth/{module_name}/{tag}/{module_name}.{artifact_type}"
    
    def check_artifact_exists(self, module_name: str, tag: str, artifact_type: str = "zip") -> bool:
        """
        检查制品是否存在
        
        Args:
            module_name: 模块名称
            tag: 版本标签
            artifact_type: 制品类型
            
        Returns:
            制品是否存在
        """
        try:
            check_url = self._build_upload_url(module_name, tag, artifact_type)
            response = self.session.head(check_url, timeout=30)
            
            exists = response.status_code == 200
            if exists:
                logger.info(f"制品已存在: {check_url}")
            
            return exists
            
        except Exception as e:
            logger.warning(f"检查制品存在性失败 {module_name}: {e}")
            return False
    
    def download_artifact(
        self,
        module_name: str,
        tag: str,
        output_path: str,
        artifact_type: str = "zip"
    ) -> bool:
        """
        下载制品
        
        Args:
            module_name: 模块名称
            tag: 版本标签
            output_path: 输出路径
            artifact_type: 制品类型
            
        Returns:
            下载是否成功
        """
        try:
            download_url = self._build_upload_url(module_name, tag, artifact_type)
            
            response = self.session.get(download_url, stream=True, timeout=1800)
            response.raise_for_status()
            
            output_path_obj = Path(output_path)
            output_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"制品下载成功: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"下载制品失败 {module_name}: {e}")
            raise NexusError(f"制品下载失败: {e}")
    
    def list_artifacts(self, module_name: str) -> list:
        """
        列出模块的所有制品版本
        
        Args:
            module_name: 模块名称
            
        Returns:
            版本列表
        """
        try:
            # 这里需要根据实际的Nexus API来实现
            # 由于原代码中没有这个功能，这里提供一个基础实现
            logger.info(f"列出制品版本: {module_name}")
            return []
            
        except Exception as e:
            logger.error(f"列出制品失败 {module_name}: {e}")
            return []
    
    def delete_artifact(self, module_name: str, tag: str, artifact_type: str = "zip") -> bool:
        """
        删除制品
        
        Args:
            module_name: 模块名称
            tag: 版本标签
            artifact_type: 制品类型
            
        Returns:
            删除是否成功
        """
        try:
            delete_url = self._build_upload_url(module_name, tag, artifact_type)
            
            response = self.session.delete(delete_url, timeout=30)
            
            if response.status_code in [200, 204, 404]:
                logger.info(f"制品删除成功: {delete_url}")
                return True
            else:
                raise NexusError(f"删除失败 (HTTP {response.status_code}): {response.text}")
                
        except Exception as e:
            logger.error(f"删除制品失败 {module_name}: {e}")
            raise NexusError(f"制品删除失败: {e}")
    
    def get_artifact_info(self, module_name: str, tag: str, artifact_type: str = "zip") -> Dict[str, Any]:
        """
        获取制品信息
        
        Args:
            module_name: 模块名称
            tag: 版本标签
            artifact_type: 制品类型
            
        Returns:
            制品信息字典
        """
        try:
            info_url = self._build_upload_url(module_name, tag, artifact_type)
            response = self.session.head(info_url, timeout=30)
            
            if response.status_code == 200:
                return {
                    'module_name': module_name,
                    'tag': tag,
                    'artifact_type': artifact_type,
                    'url': info_url,
                    'exists': True,
                    'size': response.headers.get('Content-Length'),
                    'last_modified': response.headers.get('Last-Modified'),
                    'content_type': response.headers.get('Content-Type')
                }
            else:
                return {
                    'module_name': module_name,
                    'tag': tag,
                    'artifact_type': artifact_type,
                    'url': info_url,
                    'exists': False
                }
                
        except Exception as e:
            logger.error(f"获取制品信息失败 {module_name}: {e}")
            return {
                'module_name': module_name,
                'tag': tag,
                'artifact_type': artifact_type,
                'exists': False,
                'error': str(e)
            }
    
    def test_connection(self) -> bool:
        """
        测试Nexus连接
        
        Returns:
            连接是否正常
        """
        try:
            response = self.session.get(f"{self.base_url}/", timeout=10)
            return response.status_code < 400
        except Exception as e:
            logger.error(f"Nexus连接测试失败: {e}")
            return False
