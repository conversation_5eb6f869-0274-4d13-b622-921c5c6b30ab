#!/usr/bin/env python3
# encoding: utf-8
"""
模块配置解析器
负责解析和管理 module.yml 配置文件
"""

import yaml
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass
from utils.exceptions import ConfigError
from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ModuleConfig:
    """单个模块配置"""
    name: str
    git: str
    jdk: Optional[int] = None
    node: Optional[int] = None
    build_script: str = ""
    dist_dir: Optional[str] = None
    package_name: Optional[str] = None
    only_sql: bool = False
    
    @property
    def is_java_project(self) -> bool:
        """是否为Java项目"""
        return self.jdk is not None
    
    @property
    def is_node_project(self) -> bool:
        """是否为Node.js项目"""
        return self.node is not None
    
    @property
    def project_type(self) -> str:
        """项目类型"""
        if self.is_java_project:
            return "java"
        elif self.is_node_project:
            return "node"
        else:
            return "unknown"


class ModuleConfigManager:
    """模块配置管理器"""
    
    def __init__(self, config_file: str):
        self.config_file = Path(config_file)
        self._modules: Dict[str, ModuleConfig] = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            if not self.config_file.exists():
                raise ConfigError(f"配置文件不存在: {self.config_file}")
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            if not data or 'modules' not in data:
                raise ConfigError("配置文件格式错误: 缺少 'modules' 节点")
            
            self._parse_modules(data['modules'])
            logger.info(f"成功加载 {len(self._modules)} 个模块配置")
            
        except yaml.YAMLError as e:
            raise ConfigError(f"YAML解析错误: {e}")
        except Exception as e:
            raise ConfigError(f"配置加载失败: {e}")
    
    def _parse_modules(self, modules_data: Dict[str, Any]) -> None:
        """解析模块配置"""
        for name, config in modules_data.items():
            try:
                module_config = ModuleConfig(
                    name=name,
                    git=config.get('git', ''),
                    jdk=config.get('jdk'),
                    node=config.get('node'),
                    build_script=config.get('build_script', ''),
                    dist_dir=config.get('dist_dir'),
                    package_name=config.get('package_name', name),
                    only_sql=config.get('only_sql', False)
                )
                
                # 验证配置
                self._validate_module_config(module_config)
                self._modules[name] = module_config
                
            except Exception as e:
                logger.error(f"解析模块 {name} 配置失败: {e}")
                raise ConfigError(f"模块 {name} 配置错误: {e}")
    
    def _validate_module_config(self, config: ModuleConfig) -> None:
        """验证模块配置"""
        if not config.git:
            raise ValueError("git 地址不能为空")
        
        if not config.is_java_project and not config.is_node_project:
            raise ValueError("必须指定 jdk 或 node 版本")
        
        if config.is_java_project and config.is_node_project:
            raise ValueError("不能同时指定 jdk 和 node 版本")
    
    def get_module(self, name: str) -> Optional[ModuleConfig]:
        """获取指定模块配置"""
        return self._modules.get(name)
    
    def get_all_modules(self) -> Dict[str, ModuleConfig]:
        """获取所有模块配置"""
        return self._modules.copy()
    
    def get_modules_by_type(self, project_type: str) -> List[ModuleConfig]:
        """根据项目类型获取模块列表"""
        return [
            config for config in self._modules.values()
            if config.project_type == project_type
        ]
    
    def module_exists(self, name: str) -> bool:
        """检查模块是否存在"""
        return name in self._modules
    
    def reload(self) -> None:
        """重新加载配置"""
        self._modules.clear()
        self._load_config()
        logger.info("配置已重新加载")
