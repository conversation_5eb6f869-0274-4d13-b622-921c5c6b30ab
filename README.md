# Luban DevOps Platform 2.0

## 🎯 项目简介

Luban DevOps Platform 是一个现代化的自动编译打包平台，支持Java和Node.js项目的自动化构建、打包和部署。

### ✨ 主要特性

- 🏗️ **模块化架构**: 清晰的代码结构，易于维护和扩展
- 🔧 **多项目类型支持**: 支持Java(Maven)和Node.js项目
- 🐳 **容器化部署**: 自动构建Docker镜像并推送到Harbor
- 📦 **制品管理**: 集成Nexus进行制品存储和管理
- 🔄 **Git集成**: 自动拉取代码并支持分支/标签切换
- 📊 **RESTful API**: 完整的API接口，支持前端集成
- 📝 **完善日志**: 结构化日志系统，便于问题排查
- ⚙️ **配置管理**: 灵活的配置系统，支持环境变量覆盖

## 🏗️ 架构设计

```
├── api/                    # API层
│   ├── models.py          # 数据模型
│   └── routes.py          # 路由定义
├── builders/              # 构建器模块
│   ├── base_builder.py    # 构建器基类
│   ├── java_builder.py    # Java构建器
│   ├── node_builder.py    # Node.js构建器
│   └── builder_factory.py # 构建器工厂
├── config/                # 配置管理
│   ├── settings.py        # 全局配置
│   └── module_config.py   # 模块配置解析
├── deploy/                # 部署模块
│   ├── harbor_client.py   # Harbor客户端
│   └── nexus_client.py    # Nexus客户端
├── git/                   # Git操作
│   └── git_manager.py     # Git管理器
├── services/              # 服务层
│   ├── build_service.py   # 构建服务
│   └── artifact_service.py # 制品服务
├── utils/                 # 工具模块
│   ├── exceptions.py      # 自定义异常
│   ├── logger.py          # 日志系统
│   └── file_utils.py      # 文件工具
├── main.py               # 应用入口
├── module.yml            # 模块配置文件
└── requirements.txt      # 依赖文件
```

## 🚀 快速开始

### 1. 环境准备

```bash
# Python 3.8+
python --version

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 3. 启动服务

```bash
# 开发模式
python main.py

# 生产模式
uvicorn main:app --host 0.0.0.0 --port 8000
```

## 📖 API文档

启动服务后访问: http://localhost:8000/docs

### 主要接口

#### 构建模块
```http
POST /api/v1/build
Content-Type: application/json

{
    "module_name": "ent-admin-org",
    "tag": "v1.0.0",
    "force_rebuild": false
}
```

#### 获取模块列表
```http
GET /api/v1/modules
```

#### 获取制品信息
```http
GET /api/v1/artifacts/{module_name}?tag=latest
```

#### 兼容接口
```http
GET /api/v1/pack?app=ent-admin-org&tag=latest
```

## ⚙️ 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `BASE_DIR` | 基础工作目录 | `/luban/work/plinth` |
| `HARBOR_HOST` | Harbor地址 | `harbor.luban.fit:8384` |
| `NEXUS_HOST` | Nexus地址 | `nexus.luban.fit` |
| `PUSH_HARBOR_ENABLE` | 是否推送到Harbor | `true` |
| `PUSH_NEXUS_ENABLE` | 是否推送到Nexus | `true` |

### 模块配置 (module.yml)

```yaml
modules:
  ent-admin-org:
    git: http://git.luban.fit/jishuyunying/infra/orgMgr/ent-admin-org
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  
  ent-admin-web:
    git: http://git.luban.fit/builder2.0/builder-admin-mono.git
    node: 18
    dist_dir: apps/ent-admin-web/dist
    build_script: |
      pnpm install --frozen-lockfile
      pnpm build:clean
```

## 🔧 开发指南

### 添加新的构建器

1. 继承 `BaseBuilder` 类
2. 实现 `build()` 和 `get_artifact_path()` 方法
3. 在 `BuilderFactory` 中注册新构建器

```python
from builders.base_builder import BaseBuilder

class CustomBuilder(BaseBuilder):
    def build(self, tag: str) -> str:
        # 实现构建逻辑
        pass
    
    def get_artifact_path(self) -> str:
        # 返回构建产物路径
        pass

# 注册构建器
BuilderFactory.register_builder("custom", CustomBuilder)
```

### 自定义异常处理

```python
from utils.exceptions import DevOpsError

class CustomError(DevOpsError):
    pass

try:
    # 业务逻辑
    pass
except CustomError as e:
    logger.error(f"自定义错误: {e}")
```

## 📊 监控和日志

### 日志级别

- `DEBUG`: 详细调试信息
- `INFO`: 一般信息
- `WARNING`: 警告信息
- `ERROR`: 错误信息
- `CRITICAL`: 严重错误

### 健康检查

```http
GET /api/v1/health
```

返回服务状态和各组件连接状态。

## 🔄 从旧版本迁移

### API兼容性

新版本保持了与旧版本的API兼容性：

- `/pack` 接口继续可用
- 返回格式保持一致
- 支持相同的参数

### 配置迁移

1. 保持 `module.yml` 格式不变
2. 添加环境变量配置
3. 更新启动脚本

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 🆘 故障排除

### 常见问题

1. **构建失败**: 检查构建脚本和依赖环境
2. **推送失败**: 验证Harbor/Nexus认证信息
3. **Git操作失败**: 检查网络连接和仓库权限

### 日志查看

```bash
# 查看实时日志
tail -f logs/devops.log

# 查看错误日志
grep ERROR logs/devops.log
```

---

**作者**: Claude 4.0 sonnet  
**版本**: 2.0.0  
**更新时间**: 2025-07-02
