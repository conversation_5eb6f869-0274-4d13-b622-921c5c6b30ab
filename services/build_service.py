#!/usr/bin/env python3
# encoding: utf-8
"""
构建服务
统一管理整个构建流程，协调各个组件
"""

import time
from typing import Dict, Any, Optional
from config.settings import settings
from config.module_config import ModuleConfigManager, ModuleConfig
from git.git_manager import GitManager
from builders.builder_factory import BuilderFactory
from deploy.harbor_client import HarborClient
from deploy.nexus_client import NexusClient
from services.artifact_service import ArtifactService
from utils.exceptions import (
    ModuleNotFoundError, BuildError, DeployError
)
from utils.logger import get_logger

logger = get_logger(__name__)


class BuildService:
    """构建服务主类"""
    
    def __init__(self):
        self.config_manager = ModuleConfigManager(settings.MODULE_CONFIG_FILE)
        self.git_manager = GitManager(settings.CODE_DIR)
        self.harbor_client = HarborClient()
        self.nexus_client = NexusClient()
        self.artifact_service = ArtifactService()
    
    def build_module(self, module_name: str, tag: str = "latest") -> Dict[str, Any]:
        """
        构建指定模块
        
        Args:
            module_name: 模块名称
            tag: 构建标签
            
        Returns:
            构建结果信息
        """
        start_time = time.time()
        
        try:
            # 验证模块存在
            module_config = self._get_module_config(module_name)
            
            # 检查制品是否已存在
            if self._check_artifact_exists(module_config, tag):
                return self._create_build_result(
                    module_name, tag, "skipped", 
                    message="制品已存在，跳过构建",
                    duration=time.time() - start_time
                )
            
            logger.info(f"开始构建模块: {module_name} (tag: {tag})")
            
            # 1. 克隆或更新代码
            module_dir = self._clone_or_update_code(module_config, tag)
            
            # 2. 执行构建
            artifact_path = self._execute_build(module_config, tag)
            
            # 3. 部署制品
            deployment_info = self._deploy_artifacts(module_config, tag, artifact_path)
            
            # 4. 更新配置文件
            self._update_deployment_config(module_config, tag)
            
            duration = time.time() - start_time
            logger.info(f"模块构建完成: {module_name} (耗时: {duration:.2f}s)")
            
            return self._create_build_result(
                module_name, tag, "success",
                artifact_path=artifact_path,
                deployment_info=deployment_info,
                duration=duration
            )
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"模块构建失败: {module_name} - {e}")
            
            return self._create_build_result(
                module_name, tag, "failed",
                error=str(e),
                duration=duration
            )
    
    def _get_module_config(self, module_name: str) -> ModuleConfig:
        """获取模块配置"""
        module_config = self.config_manager.get_module(module_name)
        if not module_config:
            raise ModuleNotFoundError(f"模块不存在: {module_name}")
        return module_config
    
    def _check_artifact_exists(self, module_config: ModuleConfig, tag: str) -> bool:
        """检查制品是否已存在"""
        return self.artifact_service.check_artifact_exists(module_config, tag)
    
    def _clone_or_update_code(self, module_config: ModuleConfig, tag: str) -> str:
        """克隆或更新代码"""
        logger.info(f"获取代码: {module_config.name}")
        return self.git_manager.clone_or_update(
            module_config.git,
            module_config.name,
            tag
        )
    
    def _execute_build(self, module_config: ModuleConfig, tag: str) -> str:
        """执行构建"""
        logger.info(f"执行构建: {module_config.name}")
        
        # 创建构建器
        builder = BuilderFactory.create_builder(module_config, settings.CODE_DIR)
        
        # 执行构建
        artifact_path = builder.build(tag)
        
        return artifact_path
    
    def _deploy_artifacts(
        self, 
        module_config: ModuleConfig, 
        tag: str, 
        artifact_path: str
    ) -> Dict[str, Any]:
        """部署制品"""
        logger.info(f"部署制品: {module_config.name}")
        
        deployment_info = {}
        
        try:
            if module_config.is_java_project:
                # Java项目：构建并推送Docker镜像
                image_name = self._deploy_java_project(module_config, tag, artifact_path)
                deployment_info['docker_image'] = image_name
                
            elif module_config.is_node_project:
                # Node.js项目：上传ZIP包并构建Docker镜像
                upload_success = self._deploy_node_project(module_config, tag, artifact_path)
                deployment_info['nexus_upload'] = upload_success
                
                # 构建前端Docker镜像
                image_name = self._deploy_frontend_image(module_config, tag)
                deployment_info['docker_image'] = image_name
            
            return deployment_info
            
        except Exception as e:
            raise DeployError(f"部署失败: {e}")
    
    def _deploy_java_project(self, module_config: ModuleConfig, tag: str, jar_path: str) -> str:
        """部署Java项目"""
        # 创建Dockerfile
        dockerfile_content = self.harbor_client.create_java_dockerfile(
            module_config.name, jar_path
        )
        
        # 构建并推送镜像
        return self.harbor_client.build_and_push_image(
            module_config.name,
            tag,
            str(settings.CODE_DIR / module_config.name),
            dockerfile_content=dockerfile_content
        )
    
    def _deploy_node_project(self, module_config: ModuleConfig, tag: str, zip_path: str) -> bool:
        """部署Node.js项目"""
        # 上传ZIP包到Nexus
        return self.nexus_client.upload_artifact(
            module_config.name,
            tag,
            zip_path,
            "zip"
        )
    
    def _deploy_frontend_image(self, module_config: ModuleConfig, tag: str) -> str:
        """部署前端Docker镜像"""
        # 创建前端Dockerfile
        dockerfile_content = self.harbor_client.create_frontend_dockerfile(
            module_config.name,
            module_config.dist_dir or "dist"
        )
        
        # 构建并推送镜像
        return self.harbor_client.build_and_push_image(
            module_config.name,
            tag,
            str(settings.CODE_DIR / module_config.name),
            dockerfile_content=dockerfile_content
        )
    
    def _update_deployment_config(self, module_config: ModuleConfig, tag: str) -> None:
        """更新部署配置"""
        try:
            if module_config.is_node_project:
                # 更新前端配置文件
                self._update_frontend_config(module_config.name, tag)
            
            # 更新docker-compose文件
            self._update_compose_file(module_config.name, tag)
            
        except Exception as e:
            logger.warning(f"更新配置文件失败: {e}")
    
    def _update_frontend_config(self, module_name: str, tag: str) -> None:
        """更新前端配置文件"""
        # 实现前端配置更新逻辑
        fe_config_file = f"{settings.BASE_DIR}/others/nginx/rootfs/luban/ops/fe.txt"
        
        try:
            # 读取现有配置
            fe_dict = {}
            with open(fe_config_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if ':' in line:
                        m, t = line.split(':', 1)
                        fe_dict[m] = t
            
            # 更新配置
            fe_dict[module_name] = tag
            
            # 写回配置
            with open(fe_config_file, 'w') as f:
                for k, v in sorted(fe_dict.items()):
                    f.write(f"{k}:{v}\n")
                    
            logger.info(f"更新前端配置: {module_name}:{tag}")
            
        except Exception as e:
            logger.warning(f"更新前端配置失败: {e}")
    
    def _update_compose_file(self, module_name: str, tag: str) -> None:
        """更新docker-compose文件"""
        # 实现compose文件更新逻辑
        # 这里可以根据需要实现
        logger.info(f"更新compose配置: {module_name}:{tag}")
    
    def _create_build_result(
        self,
        module_name: str,
        tag: str,
        status: str,
        message: str = "",
        artifact_path: str = "",
        deployment_info: Optional[Dict[str, Any]] = None,
        error: str = "",
        duration: float = 0.0
    ) -> Dict[str, Any]:
        """创建构建结果"""
        return {
            'module_name': module_name,
            'tag': tag,
            'status': status,
            'message': message,
            'artifact_path': artifact_path,
            'deployment_info': deployment_info or {},
            'error': error,
            'duration': duration,
            'timestamp': time.time()
        }
    
    def get_module_list(self) -> Dict[str, Any]:
        """获取模块列表"""
        modules = self.config_manager.get_all_modules()
        
        return {
            'total': len(modules),
            'modules': [
                {
                    'name': name,
                    'project_type': config.project_type,
                    'git': config.git,
                    'jdk': config.jdk,
                    'node': config.node
                }
                for name, config in modules.items()
            ]
        }
    
    def get_module_info(self, module_name: str) -> Dict[str, Any]:
        """获取模块详细信息"""
        module_config = self._get_module_config(module_name)
        
        return {
            'name': module_config.name,
            'project_type': module_config.project_type,
            'git': module_config.git,
            'jdk': module_config.jdk,
            'node': module_config.node,
            'build_script': module_config.build_script,
            'dist_dir': module_config.dist_dir,
            'package_name': module_config.package_name,
            'only_sql': module_config.only_sql
        }
