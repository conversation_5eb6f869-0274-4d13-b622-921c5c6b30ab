modules:
  ent-admin-org:
    git: http://git.luban.fit/jishuyunying/infra/orgMgr/ent-admin-org
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  ent-admin-user:
    git: http://git.luban.fit/jishuyunying/infra/usermgr/ent-admin-user
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  ent-admin-basic-data:
    git: http://git.luban.fit/jishuyunying/infra/baseData/ent-admin-basic-data
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-auth:
    git: http://git.luban.fit/jishuyunying/infra/authentication/luban-auth
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-acl:
    git: http://git.luban.fit/jishuyunying/infra/authorization/luban-acl.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  ruoyi:
    git: http://git.luban.fit/jishuyunying/infra/base-service/ruoyi.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  builder-job:
    git: http://git.luban.fit/platform2.0/be-base/builder-job.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-gateway:
    git: http://git.luban.fit/jishuyunying/infra/base-service/luban-gateway.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-process:
    git: https://git.luban.fit/jishuyunying/infra/workflow/luban-process.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-file:
    git: http://git.luban.fit/platform2.0/be-base/luban-file.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-file-convert:
    git: http://git.luban.fit/platform2.0/be-base/luban-file-convert.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-file-preview:
    git: http://git.luban.fit/platform2.0/be-base/luban-file-preview.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-license:
    git: http://git.luban.fit/jishuyunying/infra/quotaMgr/luban-license.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  ent-admin-db:
    git: http://git.luban.fit/jishuyunying/infra/ent-admin/db-init.git
    jdk: 17
    only_sql: true
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  shenyu-admin:
    git: http://git.luban.fit/jishuyunying/infra/base-service/shenyu-admin.git
    jdk: 17
    only_sql: true
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-sms:
    git: http://git.luban.fit/jishuyunying/infra/luban-sms
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-sms-mock:
    git: http://git.luban.fit/jishuyunying/infra/message-gateway/luban-sms-mock.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-signature:
    git: http://git.luban.fit/jishuyunying/infra/sign/luban-signature.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-verify:
    git: http://git.luban.fit/jishuyunying/infra/sign/luban-verify.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-infrastructure-center:
    git: https://git.luban.fit/platform2.0/be-temp/luban-infrastructure-center.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  builder-init-data:
    git: https://git.luban.fit/platform2.0/be-business/builder-init-data.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  builder-doc:
    git: https://git.luban.fit/platform2.0/be-business/builder-doc.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  builder-plan:
    git: https://git.luban.fit/platform2.0/be-business/builder-plan.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  builder-sphere:
    git: http://git.luban.fit/platform2.0/be-temp/builder_sphere.git
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  signature-admin:
    git: http://git.luban.fit/jishuyunying/infra/sign/signature.git
    node: 16
    dist_dir: dist
    build_script: |
      export COREPACK_DEFAULT_TO_LATEST=0
      export COREPACK_NPM_REGISTRY=https://nexus3.luban.fit/repository/npm
      npm install -g yarn
      yarn install
      yarn build:clean
  e-sign:
    git: http://git.luban.fit/jishuyunying/infra/sign/signature.git
    node: 16
    dist_dir: dist
    build_script: |
      export COREPACK_DEFAULT_TO_LATEST=0
      export COREPACK_NPM_REGISTRY=https://nexus3.luban.fit/repository/npm
      npm install -g yarn
      yarn install
      yarn build:clean
  general-config:
    git: http://git.luban.fit/platform2.0/fe-temp/general-config.git
    node: 18
    dist_dir: dist
    build_script: |
      yarn
      yarn release
  ent-admin-web:
    git: http://git.luban.fit/builder2.0/builder-admin-mono.git
    node: 18
    dist_dir: apps/ent-admin-web/dist
    build_script: |
      export COREPACK_DEFAULT_TO_LATEST=0
      export COREPACK_NPM_REGISTRY=https://nexus3.luban.fit/repository/npm
      pnpm install --frozen-lockfile
      pnpm build:clean
  e-sign-admin:
    git: https://git.luban.fit/chenhuaxiang/signature-admin-mono.git
    node: 18
    dist_dir: apps/signature-admin-web/dist
    build_script: |
      export COREPACK_DEFAULT_TO_LATEST=0
      export COREPACK_NPM_REGISTRY=https://nexus3.luban.fit/repository/npm
      pnpm install --frozen-lockfile
      pnpm build:clean
  sso:
    git: http://git.luban.fit/jishuyunying/infra/authentication/luban-auth-sso.git
    node: 18
    dist_dir: dist
    build_script: |
      export COREPACK_DEFAULT_TO_LATEST=0
      export COREPACK_NPM_REGISTRY=https://nexus3.luban.fit/repository/npm
      corepack enable pnpm
      pnpm install --frozen-lockfile
      pnpm build:clean
  preview:
    git: https://git.luban.fit/jishuyunying/infra/file/file-preview-web.git
    node: 18
    dist_dir: dist
    build_script: |
      export COREPACK_DEFAULT_TO_LATEST=0
      export COREPACK_NPM_REGISTRY=https://nexus3.luban.fit/repository/npm
      corepack enable pnpm
      pnpm install --frozen-lockfile
      pnpm build:clean
  ywgl-admin-web:
    git: http://git.luban.fit/jishuyunying/other-proj/ywgl-admin-mono.git
    node: 18
    dist_dir: apps/ywgl-admin-web/dist
    build_script: |
      export COREPACK_DEFAULT_TO_LATEST=0
      export COREPACK_NPM_REGISTRY=https://nexus3.luban.fit/repository/npm
      corepack enable pnpm
      pnpm -v
      pnpm install --frozen-lockfile
      pnpm build:clean
  main-builder-web:
    git: http://git.luban.fit/platform2.0/fe-temp/mainbuilder.git
    node: 16
    dist_dir: dist
    build_script: |
      corepack enable
      yarn config set registry http://nexus3.luban.fit/repository/npm
      yarn config set @luban:registry http://nexus3.luban.fit/repository/npm-luban
      yarn config set @iworks:registry http://nexus3.luban.fit/repository/npm-iworks
      yarn config set @lubango:registry http://nexus3.luban.fit/repository/npm-lubango
      yarn config set @motor:registry http://nexus3.luban.fit/repository/npm-motor
      yarn install
      yarn build
  luban-acl-admin:
    git: http://git.luban.fit/jishuyunying/infra/authorization/luban-acl-admin.git
    node: 18
    dist_dir: dist
    build_script: |
      pnpm install --frozen-lockfile
      pnpm build:clean
  luban-register:
    git: http://git.luban.fit/jishuyunying/infra/authentication/luban-auth-fe.git
    node: 18
    dist_dir: dist
    build_script: |
      pnpm config set registry http://nexus3.luban.fit/repository/npm
      pnpm config set @luban:registry http://nexus3.luban.fit/repository/npm-luban
      pnpm config set @iworks:registry http://nexus3.luban.fit/repository/npm-iworks
      pnpm config set @lubango:registry http://nexus3.luban.fit/repository/npm-lubango
      pnpm config set @motor:registry http://nexus3.luban.fit/repository/npm-motor
      pnpm install --frozen-lockfile
      pnpm build:web
  file-online-preview:
    git: http://git.luban.fit/jishuyunying/infra/file/file-preview.git
    node: 18
    dist_dir: server/src/main/resources/static
    build_script: |
  luban-auth-plugin-4-luban:
    git: https://git.luban.fit/gujunyi/luban-auth-plugin-4-luban
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  worker-registry:
    git: https://git.luban.fit/lb_zhuyou/ecs-adapters
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  motor-project:
    git: https://git.luban.fit/lb_zhuyou/motor-cloud-service
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true    
  luban-oss-flow:
    git: https://git.luban.fit/gujunyi/luban-oss-flow
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true
  luban-quota:
    git: https://git.luban.fit/jishuyunying/infra/quotaMgr/luban-quota
    jdk: 17
    build_script: |
      source ../../jdk-setenv.sh 17
      mvn -q -gs ../../maven.xml clean package -DskipTests=true