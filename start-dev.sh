#!/bin/bash

echo "=== Luban DevOps Platform 2.0 开发模式启动脚本 ==="
echo

# 设置开发环境变量
export DEBUG=true
export LOG_LEVEL=DEBUG
export PUSH_HARBOR_ENABLE=false
export PUSH_NEXUS_ENABLE=false

echo "🔧 开发模式配置:"
echo "   - DEBUG: $DEBUG"
echo "   - LOG_LEVEL: $LOG_LEVEL"
echo "   - PUSH_HARBOR_ENABLE: $PUSH_HARBOR_ENABLE"
echo "   - PUSH_NEXUS_ENABLE: $PUSH_NEXUS_ENABLE"
echo

# 检查Python版本
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo "❌ 错误: 未找到Python，请先安装Python 3.8+"
        exit 1
    fi
    
    echo "✅ 找到Python: $($PYTHON_CMD --version)"
}

# 检查并创建虚拟环境
setup_venv() {
    if [ ! -d ".venv" ]; then
        echo "📦 创建虚拟环境..."
        $PYTHON_CMD -m venv .venv
        if [ $? -ne 0 ]; then
            echo "❌ 创建虚拟环境失败"
            exit 1
        fi
    fi
    
    echo "🔧 激活虚拟环境..."
    source .venv/bin/activate
    
    if [ $? -ne 0 ]; then
        echo "❌ 激活虚拟环境失败"
        exit 1
    fi
}

# 安装依赖（包括开发依赖）
install_dependencies() {
    echo "📋 检查依赖..."
    
    # 检查requirements.txt是否存在
    if [ ! -f "requirements.txt" ]; then
        echo "❌ 未找到requirements.txt文件"
        exit 1
    fi
    
    # 安装依赖
    echo "📦 安装Python依赖..."
    pip install --upgrade pip
    pip install -r requirements.txt
    
    # 安装开发依赖
    pip install pytest pytest-asyncio httpx
    
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
}

# 创建必要目录
create_directories() {
    echo "📁 创建必要目录..."
    mkdir -p logs code tests
    echo "✅ 目录创建完成"
}

# 启动应用
start_app() {
    echo "🚀 启动Luban DevOps Platform (开发模式)..."
    echo "📍 访问地址: http://localhost:8000"
    echo "📖 API文档: http://localhost:8000/docs"
    echo "🔄 文件变更自动重载已启用"
    echo "❌ 按 Ctrl+C 停止服务"
    echo
    
    python main.py
}

# 主流程
main() {
    check_python
    setup_venv
    install_dependencies
    create_directories
    start_app
}

# 错误处理
trap 'echo ""; echo "👋 开发服务已停止"; exit 0' INT

# 执行主流程
main
