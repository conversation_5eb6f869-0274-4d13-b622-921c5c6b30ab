#!/usr/bin/env python3
# encoding: utf-8
"""
制品管理服务
统一管理构建产物的检查、上传、下载等操作
"""

from typing import Dict, Any, List, Optional
from config.module_config import ModuleConfig
from deploy.harbor_client import HarborClient
from deploy.nexus_client import NexusClient
from utils.logger import get_logger

logger = get_logger(__name__)


class ArtifactService:
    """制品管理服务"""
    
    def __init__(self):
        self.harbor_client = HarborClient()
        self.nexus_client = NexusClient()
    
    def check_artifact_exists(self, module_config: ModuleConfig, tag: str) -> bool:
        """
        检查制品是否存在
        
        Args:
            module_config: 模块配置
            tag: 版本标签
            
        Returns:
            制品是否存在
        """
        try:
            if module_config.is_java_project:
                # Java项目检查Docker镜像
                return self.harbor_client.check_image_exists(module_config.name, tag)
            
            elif module_config.is_node_project:
                # Node.js项目检查Nexus制品
                return self.nexus_client.check_artifact_exists(module_config.name, tag, "zip")
            
            return False
            
        except Exception as e:
            logger.warning(f"检查制品存在性失败 {module_config.name}: {e}")
            return False
    
    def get_artifact_info(self, module_config: ModuleConfig, tag: str) -> Dict[str, Any]:
        """
        获取制品信息
        
        Args:
            module_config: 模块配置
            tag: 版本标签
            
        Returns:
            制品信息字典
        """
        info = {
            'module_name': module_config.name,
            'tag': tag,
            'project_type': module_config.project_type,
            'artifacts': []
        }
        
        try:
            if module_config.is_java_project:
                # Java项目的Docker镜像信息
                docker_info = self.harbor_client.get_image_info(module_config.name, tag)
                docker_info['type'] = 'docker_image'
                docker_info['exists'] = self.harbor_client.check_image_exists(module_config.name, tag)
                info['artifacts'].append(docker_info)
            
            elif module_config.is_node_project:
                # Node.js项目的Nexus制品信息
                nexus_info = self.nexus_client.get_artifact_info(module_config.name, tag, "zip")
                nexus_info['type'] = 'nexus_artifact'
                info['artifacts'].append(nexus_info)
                
                # 前端项目也可能有Docker镜像
                docker_info = self.harbor_client.get_image_info(module_config.name, tag)
                docker_info['type'] = 'docker_image'
                docker_info['exists'] = self.harbor_client.check_image_exists(module_config.name, tag)
                info['artifacts'].append(docker_info)
            
            return info
            
        except Exception as e:
            logger.error(f"获取制品信息失败 {module_config.name}: {e}")
            info['error'] = str(e)
            return info
    
    def list_module_artifacts(self, module_name: str) -> List[Dict[str, Any]]:
        """
        列出模块的所有制品版本
        
        Args:
            module_name: 模块名称
            
        Returns:
            制品版本列表
        """
        try:
            # 从Nexus获取版本列表
            nexus_artifacts = self.nexus_client.list_artifacts(module_name)
            
            # 这里可以扩展为同时查询Harbor的镜像标签
            # harbor_tags = self.harbor_client.list_image_tags(module_name)
            
            return nexus_artifacts
            
        except Exception as e:
            logger.error(f"列出制品失败 {module_name}: {e}")
            return []
    
    def delete_artifact(self, module_config: ModuleConfig, tag: str) -> Dict[str, Any]:
        """
        删除制品
        
        Args:
            module_config: 模块配置
            tag: 版本标签
            
        Returns:
            删除结果
        """
        result = {
            'module_name': module_config.name,
            'tag': tag,
            'deleted_artifacts': [],
            'errors': []
        }
        
        try:
            if module_config.is_node_project:
                # 删除Nexus制品
                if self.nexus_client.delete_artifact(module_config.name, tag, "zip"):
                    result['deleted_artifacts'].append('nexus_zip')
                else:
                    result['errors'].append('nexus_zip_deletion_failed')
            
            # 注意：通常不建议自动删除Docker镜像，因为可能被其他地方使用
            # 这里只是提供接口，实际使用时需要谨慎
            
            return result
            
        except Exception as e:
            logger.error(f"删除制品失败 {module_config.name}: {e}")
            result['errors'].append(str(e))
            return result
    
    def download_artifact(
        self,
        module_config: ModuleConfig,
        tag: str,
        output_dir: str,
        artifact_type: str = "zip"
    ) -> Optional[str]:
        """
        下载制品
        
        Args:
            module_config: 模块配置
            tag: 版本标签
            output_dir: 输出目录
            artifact_type: 制品类型
            
        Returns:
            下载的文件路径，失败返回None
        """
        try:
            if module_config.is_node_project and artifact_type == "zip":
                output_path = f"{output_dir}/{module_config.name}-{tag}.zip"
                
                if self.nexus_client.download_artifact(
                    module_config.name, tag, output_path, artifact_type
                ):
                    return output_path
            
            return None
            
        except Exception as e:
            logger.error(f"下载制品失败 {module_config.name}: {e}")
            return None
    
    def get_artifact_statistics(self) -> Dict[str, Any]:
        """
        获取制品统计信息
        
        Returns:
            统计信息字典
        """
        stats = {
            'total_modules': 0,
            'java_modules': 0,
            'node_modules': 0,
            'docker_images': 0,
            'nexus_artifacts': 0,
            'last_updated': None
        }
        
        try:
            # 这里可以实现具体的统计逻辑
            # 由于需要遍历所有模块和版本，可能比较耗时
            # 建议在后台定期更新统计信息
            
            return stats
            
        except Exception as e:
            logger.error(f"获取制品统计失败: {e}")
            return stats
    
    def cleanup_old_artifacts(
        self,
        module_name: str,
        keep_count: int = 10
    ) -> Dict[str, Any]:
        """
        清理旧制品
        
        Args:
            module_name: 模块名称
            keep_count: 保留的版本数量
            
        Returns:
            清理结果
        """
        result = {
            'module_name': module_name,
            'cleaned_count': 0,
            'errors': []
        }
        
        try:
            # 获取所有版本
            artifacts = self.list_module_artifacts(module_name)
            
            if len(artifacts) <= keep_count:
                logger.info(f"模块 {module_name} 制品数量未超过保留限制")
                return result
            
            # 排序并删除旧版本
            # 这里需要根据实际的制品信息结构来实现排序逻辑
            # artifacts.sort(key=lambda x: x['created_time'], reverse=True)
            # old_artifacts = artifacts[keep_count:]
            
            # for artifact in old_artifacts:
            #     # 删除制品的逻辑
            #     pass
            
            return result
            
        except Exception as e:
            logger.error(f"清理旧制品失败 {module_name}: {e}")
            result['errors'].append(str(e))
            return result
    
    def validate_artifact_integrity(
        self,
        module_config: ModuleConfig,
        tag: str
    ) -> Dict[str, Any]:
        """
        验证制品完整性
        
        Args:
            module_config: 模块配置
            tag: 版本标签
            
        Returns:
            验证结果
        """
        result = {
            'module_name': module_config.name,
            'tag': tag,
            'valid': True,
            'checks': [],
            'errors': []
        }
        
        try:
            # 检查制品是否存在
            exists = self.check_artifact_exists(module_config, tag)
            result['checks'].append({
                'name': 'artifact_exists',
                'passed': exists,
                'message': '制品存在性检查'
            })
            
            if not exists:
                result['valid'] = False
                result['errors'].append('制品不存在')
                return result
            
            # 可以添加更多验证逻辑
            # 例如：文件大小检查、校验和验证等
            
            return result
            
        except Exception as e:
            logger.error(f"验证制品完整性失败 {module_config.name}: {e}")
            result['valid'] = False
            result['errors'].append(str(e))
            return result
