# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=true

# 基础路径配置 (Windows路径示例)
BASE_DIR=D:/py-workspace/devops
CODE_DIR=D:/py-workspace/devops/code
NGINX_PKG_DIR=D:/py-workspace/devops/others/nginx/pkg/
DOCKER_TEMPLATE_DIR=D:/py-workspace/devops/frontend
COMPOSE_FILE=D:/py-workspace/devops/docker-compose.yml
MODULE_CONFIG_FILE=D:/py-workspace/devops/module.yml

# Harbor配置
HARBOR_HOST=harbor.luban.fit:8384
HARBOR_PROJECT=plinth
HARBOR_USERNAME=
HARBOR_PASSWORD=

# Nexus配置
NEXUS_HOST=nexus.luban.fit
NEXUS_REPOSITORY=luban-raw
NEXUS_USERNAME=
NEXUS_PASSWORD=

# 功能开关 (开发环境暂时关闭推送)
PUSH_HARBOR_ENABLE=false
PUSH_NEXUS_ENABLE=false

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/devops.log
