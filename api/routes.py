#!/usr/bin/env python3
# encoding: utf-8
"""
API路由定义
定义所有的HTTP接口端点
"""

import time
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Query, Path
from fastapi.responses import JSONResponse

from api.models import (
    BuildRequest, BuildResponse, ModuleListResponse, 
    ArtifactResponse, HealthResponse, ErrorResponse,
    StatisticsResponse, BuildStatus
)
from services.build_service import BuildService
from utils.exceptions import (
    ModuleNotFoundError, BuildError, DeployError,
    UnsupportedProjectTypeError
)
from utils.logger import get_logger

logger = get_logger(__name__)

# 创建路由器
router = APIRouter()

# 创建服务实例
build_service = BuildService()


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查接口"""
    try:
        # 检查各个服务的连接状态
        services_status = {
            "nexus": build_service.nexus_client.test_connection(),
            "git": True,  # Git操作通常不需要连接检查
            "docker": True  # Docker连接检查可以通过docker info实现
        }
        
        return HealthResponse(
            status="healthy",
            version="2.0.0",
            timestamp=time.time(),
            services=services_status
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="服务不健康")


@router.post("/build", response_model=BuildResponse)
async def build_module(request: BuildRequest):
    """构建模块接口"""
    try:
        logger.info(f"收到构建请求: {request.module_name} (tag: {request.tag})")
        
        result = build_service.build_module(request.module_name, request.tag)
        
        return BuildResponse(**result)
        
    except ModuleNotFoundError as e:
        logger.error(f"模块不存在: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    
    except (BuildError, DeployError, UnsupportedProjectTypeError) as e:
        logger.error(f"构建失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"构建过程中发生未知错误: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/build/{module_name}")
async def get_build_status(
    module_name: str = Path(..., description="模块名称"),
    tag: str = Query("latest", description="构建标签")
):
    """获取构建状态（兼容原API）"""
    try:
        # 检查制品是否存在
        module_config = build_service.config_manager.get_module(module_name)
        if not module_config:
            raise HTTPException(status_code=404, detail=f"模块不存在: {module_name}")
        
        exists = build_service.artifact_service.check_artifact_exists(module_config, tag)
        
        if exists:
            return {"status": "exists", "message": f"{module_name}:{tag} 制品已存在"}
        else:
            return {"status": "not_found", "message": f"{module_name}:{tag} 制品不存在"}
            
    except Exception as e:
        logger.error(f"获取构建状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/modules", response_model=ModuleListResponse)
async def list_modules():
    """获取模块列表"""
    try:
        result = build_service.get_module_list()
        return ModuleListResponse(**result)
        
    except Exception as e:
        logger.error(f"获取模块列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取模块列表失败")


@router.get("/modules/{module_name}")
async def get_module_info(module_name: str = Path(..., description="模块名称")):
    """获取模块详细信息"""
    try:
        result = build_service.get_module_info(module_name)
        return result
        
    except ModuleNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    
    except Exception as e:
        logger.error(f"获取模块信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取模块信息失败")


@router.get("/artifacts/{module_name}", response_model=ArtifactResponse)
async def get_artifact_info(
    module_name: str = Path(..., description="模块名称"),
    tag: str = Query("latest", description="版本标签")
):
    """获取制品信息"""
    try:
        module_config = build_service.config_manager.get_module(module_name)
        if not module_config:
            raise HTTPException(status_code=404, detail=f"模块不存在: {module_name}")
        
        result = build_service.artifact_service.get_artifact_info(module_config, tag)
        return ArtifactResponse(**result)
        
    except Exception as e:
        logger.error(f"获取制品信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取制品信息失败")


@router.delete("/artifacts/{module_name}")
async def delete_artifact(
    module_name: str = Path(..., description="模块名称"),
    tag: str = Query(..., description="版本标签")
):
    """删除制品"""
    try:
        module_config = build_service.config_manager.get_module(module_name)
        if not module_config:
            raise HTTPException(status_code=404, detail=f"模块不存在: {module_name}")
        
        result = build_service.artifact_service.delete_artifact(module_config, tag)
        return result
        
    except Exception as e:
        logger.error(f"删除制品失败: {e}")
        raise HTTPException(status_code=500, detail="删除制品失败")


@router.get("/statistics", response_model=StatisticsResponse)
async def get_statistics():
    """获取统计信息"""
    try:
        result = build_service.artifact_service.get_artifact_statistics()
        return StatisticsResponse(**result)
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")


@router.post("/config/reload")
async def reload_config():
    """重新加载配置"""
    try:
        build_service.config_manager.reload()
        return {"message": "配置重新加载成功"}
        
    except Exception as e:
        logger.error(f"重新加载配置失败: {e}")
        raise HTTPException(status_code=500, detail="重新加载配置失败")


# 兼容原有的pack接口
@router.get("/pack")
async def pack_legacy(
    app: str = Query(..., description="应用名称"),
    tag: str = Query("latest", description="标签")
):
    """兼容原有的pack接口"""
    try:
        logger.info(f"收到legacy构建请求: {app} (tag: {tag})")
        
        # 检查制品是否已存在
        module_config = build_service.config_manager.get_module(app)
        if not module_config:
            return {"error": f"模块不存在: {app}"}
        
        if build_service.artifact_service.check_artifact_exists(module_config, tag):
            return {app + tag + " 包已存在"}
        
        # 执行构建
        result = build_service.build_module(app, tag)
        
        if result['status'] == 'success':
            if module_config.is_java_project:
                return f"harbor.luban.fit:8384/plinth/{app}:{tag}"
            else:
                return f"构建成功: {app}:{tag}"
        else:
            return {"error": result.get('error', '构建失败')}
            
    except Exception as e:
        logger.error(f"Legacy构建失败: {e}")
        return {"error": str(e)}


# 全局异常处理器
@router.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="内部服务器错误",
            details={"exception": str(exc)},
            timestamp=time.time()
        ).dict()
    )
