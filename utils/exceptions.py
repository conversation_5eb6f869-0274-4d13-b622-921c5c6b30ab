#!/usr/bin/env python3
# encoding: utf-8
"""
自定义异常类
统一管理系统中的所有异常类型
"""


class DevOpsError(Exception):
    """DevOps平台基础异常类"""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class ConfigError(DevOpsError):
    """配置相关异常"""
    pass


class GitError(DevOpsError):
    """Git操作异常"""
    pass


class GitCheckoutError(GitError):
    """Git切换分支/标签异常"""
    pass


class BuildError(DevOpsError):
    """构建异常"""
    pass


class FeBuildError(BuildError):
    """前端构建异常"""
    pass


class BeBuildError(BuildError):
    """后端构建异常"""
    pass


class DeployError(DevOpsError):
    """部署异常"""
    pass


class HarborError(DeployError):
    """Harbor相关异常"""
    pass


class NexusError(DeployError):
    """Nexus相关异常"""
    pass


class ArtifactError(DevOpsError):
    """制品相关异常"""
    pass


class ValidationError(DevOpsError):
    """验证异常"""
    pass


class ModuleNotFoundError(DevOpsError):
    """模块不存在异常"""
    pass


class UnsupportedProjectTypeError(DevOpsError):
    """不支持的项目类型异常"""
    pass
