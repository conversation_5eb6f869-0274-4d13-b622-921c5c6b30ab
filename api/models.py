#!/usr/bin/env python3
# encoding: utf-8
"""
API数据模型
定义请求和响应的数据结构
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class ProjectType(str, Enum):
    """项目类型枚举"""
    JAVA = "java"
    NODE = "node"
    UNKNOWN = "unknown"


class BuildStatus(str, Enum):
    """构建状态枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"
    RUNNING = "running"


class BuildRequest(BaseModel):
    """构建请求模型"""
    module_name: str = Field(..., description="模块名称")
    tag: str = Field(default="latest", description="构建标签")
    force_rebuild: bool = Field(default=False, description="是否强制重新构建")


class BuildResponse(BaseModel):
    """构建响应模型"""
    module_name: str
    tag: str
    status: BuildStatus
    message: str = ""
    artifact_path: str = ""
    deployment_info: Dict[str, Any] = {}
    error: str = ""
    duration: float = 0.0
    timestamp: float


class ModuleInfo(BaseModel):
    """模块信息模型"""
    name: str
    project_type: ProjectType
    git: str
    jdk: Optional[int] = None
    node: Optional[int] = None
    build_script: str = ""
    dist_dir: Optional[str] = None
    package_name: Optional[str] = None
    only_sql: bool = False


class ModuleListResponse(BaseModel):
    """模块列表响应模型"""
    total: int
    modules: List[ModuleInfo]


class ArtifactInfo(BaseModel):
    """制品信息模型"""
    type: str
    exists: bool
    url: Optional[str] = None
    size: Optional[str] = None
    last_modified: Optional[str] = None
    content_type: Optional[str] = None


class ArtifactResponse(BaseModel):
    """制品响应模型"""
    module_name: str
    tag: str
    project_type: ProjectType
    artifacts: List[ArtifactInfo]
    error: Optional[str] = None


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    version: str
    timestamp: float
    services: Dict[str, bool] = {}


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: float


class StatisticsResponse(BaseModel):
    """统计信息响应模型"""
    total_modules: int
    java_modules: int
    node_modules: int
    docker_images: int
    nexus_artifacts: int
    last_updated: Optional[float] = None
