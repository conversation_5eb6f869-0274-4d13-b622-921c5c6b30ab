#!/usr/bin/env python3
# encoding: utf-8
"""
构建器工厂
根据项目类型创建相应的构建器实例
"""

from typing import Type
from config.module_config import ModuleConfig
from builders.base_builder import BaseBuilder
from builders.java_builder import JavaBuilder
from builders.node_builder import NodeBuilder
from utils.exceptions import UnsupportedProjectTypeError
from utils.logger import get_logger

logger = get_logger(__name__)


class BuilderFactory:
    """构建器工厂类"""
    
    # 构建器映射表
    _builders = {
        'java': JavaBuilder,
        'node': NodeBuilder
    }
    
    @classmethod
    def create_builder(cls, module_config: ModuleConfig, work_dir: str) -> BaseBuilder:
        """
        创建构建器实例
        
        Args:
            module_config: 模块配置
            work_dir: 工作目录
            
        Returns:
            构建器实例
            
        Raises:
            UnsupportedProjectTypeError: 不支持的项目类型
        """
        project_type = module_config.project_type
        
        if project_type not in cls._builders:
            raise UnsupportedProjectTypeError(
                f"不支持的项目类型: {project_type}. "
                f"支持的类型: {list(cls._builders.keys())}"
            )
        
        builder_class = cls._builders[project_type]
        logger.info(f"创建 {project_type} 构建器: {module_config.name}")
        
        return builder_class(module_config, work_dir)
    
    @classmethod
    def register_builder(cls, project_type: str, builder_class: Type[BaseBuilder]) -> None:
        """
        注册新的构建器类型
        
        Args:
            project_type: 项目类型
            builder_class: 构建器类
        """
        if not issubclass(builder_class, BaseBuilder):
            raise ValueError(f"构建器类必须继承自 BaseBuilder: {builder_class}")
        
        cls._builders[project_type] = builder_class
        logger.info(f"注册构建器: {project_type} -> {builder_class.__name__}")
    
    @classmethod
    def get_supported_types(cls) -> list:
        """
        获取支持的项目类型列表
        
        Returns:
            支持的项目类型列表
        """
        return list(cls._builders.keys())
    
    @classmethod
    def is_supported(cls, project_type: str) -> bool:
        """
        检查是否支持指定的项目类型
        
        Args:
            project_type: 项目类型
            
        Returns:
            是否支持
        """
        return project_type in cls._builders
