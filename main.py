#!/usr/bin/env python3
# encoding: utf-8
"""
Luban DevOps - 自动编译打包平台
重构版本 - 模块化架构

作者: Claude 4.0 sonnet
"""

import time
import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from api.routes import router
from api.models import ErrorResponse
from utils.logger import get_logger
from config.settings import settings

# 初始化日志系统
logger = get_logger(__name__)

def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    app = FastAPI(
        title="Luban DevOps Platform",
        description="自动编译打包平台 - 重构版",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 全局异常处理器
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """全局异常处理器"""
        logger.error(f"未处理的异常: {exc}")

        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                error="内部服务器错误",
                details={"exception": str(exc)},
                timestamp=time.time()
            ).dict()
        )

    # 注册路由
    app.include_router(router, prefix="/api/v1")

    # 兼容原有接口
    app.include_router(router, prefix="")

    return app

app = create_app()

if __name__ == '__main__':
    logger.info("启动 Luban DevOps Platform...")
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
