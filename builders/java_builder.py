#!/usr/bin/env python3
# encoding: utf-8
"""
Java项目构建器
专门处理Java/Maven项目的构建
"""

import os
from pathlib import Path
from typing import Dict, Any
from builders.base_builder import BaseBuilder
from utils.file_utils import find_archive_file
from utils.exceptions import BeBuildError
from utils.logger import get_logger

logger = get_logger(__name__)


class JavaBuilder(BaseBuilder):
    """Java项目构建器"""
    
    def __init__(self, module_config, work_dir: str):
        super().__init__(module_config, work_dir)
        self.jdk_version = module_config.jdk
        self.package_name = module_config.package_name or module_config.name
        self.only_sql = module_config.only_sql
    
    def build(self, tag: str) -> str:
        """
        执行Java项目构建
        
        Args:
            tag: 构建标签
            
        Returns:
            构建产物路径
        """
        try:
            # 执行构建前钩子
            self.pre_build_hook(tag)
            
            # 准备构建脚本
            build_script = self._prepare_build_script()
            
            # 执行构建
            self._execute_build_script(build_script, tag)
            
            # 查找构建产物
            artifact_path = self.get_artifact_path()
            
            # 执行构建后钩子
            self.post_build_hook(tag, artifact_path)
            
            return artifact_path
            
        except Exception as e:
            logger.error(f"Java构建失败 {self.config.name}: {e}")
            raise BeBuildError(f"Java构建失败: {e}")
    
    def _prepare_build_script(self) -> str:
        """
        准备Java构建脚本
        
        Returns:
            构建脚本内容
        """
        # 基础脚本：设置JDK环境
        base_script = f"source ../../jdk-setenv.sh {self.jdk_version}\n"
        
        # 添加用户自定义构建脚本
        if self.config.build_script:
            return base_script + self.config.build_script
        else:
            # 默认Maven构建脚本
            default_script = "mvn -q -gs ../../maven.xml clean package -DskipTests=true"
            return base_script + default_script
    
    def get_artifact_path(self) -> str:
        """
        获取Java构建产物路径
        
        Returns:
            JAR文件路径
        """
        try:
            return find_archive_file(
                search_dir=str(self.module_dir),
                module_name=self.config.name,
                file_type="jar",
                package_name=self.package_name
            )
        except Exception as e:
            raise BeBuildError(f"未找到Java构建产物: {e}")
    
    def _prepare_build_environment(self) -> Dict[str, str]:
        """
        准备Java构建环境变量
        
        Returns:
            环境变量字典
        """
        env = super()._prepare_build_environment()
        env.update({
            'JAVA_VERSION': str(self.jdk_version),
            'PACKAGE_NAME': self.package_name,
            'MAVEN_OPTS': '-Xmx2g -XX:MaxPermSize=512m'
        })
        return env
    
    def get_build_info(self) -> Dict[str, Any]:
        """
        获取Java构建信息
        
        Returns:
            构建信息字典
        """
        info = super().get_build_info()
        info.update({
            'jdk_version': self.jdk_version,
            'package_name': self.package_name,
            'only_sql': self.only_sql,
            'build_tool': 'maven'
        })
        return info
    
    def pre_build_hook(self, tag: str) -> None:
        """Java构建前钩子"""
        super().pre_build_hook(tag)
        
        # 检查Maven配置文件
        maven_config = Path("../../maven.xml")
        if not maven_config.exists():
            logger.warning("Maven配置文件不存在: ../../maven.xml")
        
        # 检查JDK设置脚本
        jdk_script = Path("../../jdk-setenv.sh")
        if not jdk_script.exists():
            logger.warning("JDK设置脚本不存在: ../../jdk-setenv.sh")
        
        logger.info(f"使用JDK版本: {self.jdk_version}")
        if self.only_sql:
            logger.info("仅SQL模式构建")
    
    def post_build_hook(self, tag: str, artifact_path: str) -> None:
        """Java构建后钩子"""
        super().post_build_hook(tag, artifact_path)
        
        # 验证JAR文件
        jar_path = Path(artifact_path)
        if jar_path.exists():
            file_size = jar_path.stat().st_size
            logger.info(f"JAR文件大小: {file_size / 1024 / 1024:.2f} MB")
        else:
            raise BeBuildError(f"构建产物不存在: {artifact_path}")
    
    def clean_build_cache(self) -> None:
        """清理构建缓存"""
        target_dir = self.module_dir / "target"
        if target_dir.exists():
            import shutil
            shutil.rmtree(target_dir)
            logger.info(f"已清理构建缓存: {target_dir}")
    
    def get_dependencies_info(self) -> Dict[str, Any]:
        """
        获取依赖信息
        
        Returns:
            依赖信息字典
        """
        pom_file = self.module_dir / "pom.xml"
        if not pom_file.exists():
            return {}
        
        # 这里可以解析pom.xml获取依赖信息
        # 为简化实现，暂时返回基础信息
        return {
            'pom_exists': True,
            'pom_path': str(pom_file)
        }
