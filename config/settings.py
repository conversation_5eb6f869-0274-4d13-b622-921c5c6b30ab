#!/usr/bin/env python3
# encoding: utf-8
"""
全局配置管理模块
统一管理所有系统配置，支持环境变量覆盖
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """全局配置类"""
    
    # 服务配置
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # 基础路径配置
    BASE_DIR: str = Field(default="/luban/work/plinth", env="BASE_DIR")
    CODE_DIR: str = Field(default="", env="CODE_DIR")
    NGINX_PKG_DIR: str = Field(default="", env="NGINX_PKG_DIR")
    DOCKER_TEMPLATE_DIR: str = Field(default="", env="DOCKER_TEMPLATE_DIR")
    COMPOSE_FILE: str = Field(default="/luban/work/plinth-compose/docker-compose.yml", env="COMPOSE_FILE")
    
    # 模块配置文件
    MODULE_CONFIG_FILE: str = Field(default="", env="MODULE_CONFIG_FILE")
    
    # Harbor配置
    HARBOR_HOST: str = Field(default="harbor.luban.fit:8384", env="HARBOR_HOST")
    HARBOR_PROJECT: str = Field(default="plinth", env="HARBOR_PROJECT")
    HARBOR_USERNAME: Optional[str] = Field(default=None, env="HARBOR_USERNAME")
    HARBOR_PASSWORD: Optional[str] = Field(default=None, env="HARBOR_PASSWORD")
    
    # Nexus配置
    NEXUS_HOST: str = Field(default="nexus.luban.fit", env="NEXUS_HOST")
    NEXUS_REPOSITORY: str = Field(default="luban-raw", env="NEXUS_REPOSITORY")
    NEXUS_USERNAME: Optional[str] = Field(default=None, env="NEXUS_USER")
    NEXUS_PASSWORD: Optional[str] = Field(default=None, env="NEXUS_PASSWORD")
    
    # 功能开关
    PUSH_HARBOR_ENABLE: bool = Field(default=True, env="PUSH_HARBOR_ENABLE")
    PUSH_NEXUS_ENABLE: bool = Field(default=True, env="PUSH_NEXUS_ENABLE")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: Optional[str] = Field(default=None, env="LOG_FILE")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 动态计算依赖路径
        if not self.CODE_DIR:
            self.CODE_DIR = f"{self.BASE_DIR}/code"
        if not self.NGINX_PKG_DIR:
            self.NGINX_PKG_DIR = f"{self.BASE_DIR}/others/nginx/pkg/"
        if not self.DOCKER_TEMPLATE_DIR:
            self.DOCKER_TEMPLATE_DIR = f"{self.BASE_DIR}/frontend"
        if not self.MODULE_CONFIG_FILE:
            self.MODULE_CONFIG_FILE = f"{self.BASE_DIR}/module.yml"
    
    @property
    def harbor_registry_url(self) -> str:
        """Harbor镜像仓库完整URL"""
        return f"{self.HARBOR_HOST}/{self.HARBOR_PROJECT}"
    
    @property
    def nexus_base_url(self) -> str:
        """Nexus基础URL"""
        return f"https://{self.NEXUS_HOST}/repository/{self.NEXUS_REPOSITORY}"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 全局配置实例
settings = Settings()
