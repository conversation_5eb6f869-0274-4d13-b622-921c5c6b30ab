#!/usr/bin/env python3
# encoding: utf-8

import os

import tornado
import yaml
import subprocess
import requests
from fastapi import FastAPI

basedir = "/luban/work/plinth"
nginx_pkg_dir = f"{basedir}/others/nginx/pkg/"
docker_template_dir = f"{basedir}/frontend"
compose_file = "/luban/work/plinth-compose/docker-compose.yml"

builder_modules_file = f"{basedir}/module.yml"
# builder_modules = None
builder_modules = yaml.safe_load(open(builder_modules_file))["modules"]
builder_module_dir = f"{basedir}/code"
has_fe_build = None
push_harbor_enable = 1
push_nexus_enable = 1

app = FastAPI()

class BuildError(Exception):
    """build pkg or image error"""


class GitError(Exception):
    """git command error"""


class GitCheckoutError(Exception):
    """git checkout error"""


class FeBuildError(Exception):
    """Fe build error"""


def get_git_tag():
    result = subprocess.run(["git", "tag", "--points-at=HEAD"], stdout=subprocess.PIPE)
    tag = result.stdout.strip().decode().split("\n")[0] or "latest"
    return tag


def git_checkout(tag):
    cmd = f"git checkout {tag}"
    result = subprocess.run(
        cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, encoding="utf8"
    )
    if result.returncode:
        print(f"Switching branch or tag error: {cmd}")
        raise GitCheckoutError(result.stderr)


def is_branch():
    result = subprocess.run(
        "git status -s -b",
        shell=True,
        check=True,
        stdout=subprocess.PIPE,
        encoding="utf8",
    )
    if "## HEAD (no branch)" in result.stdout:
        return False
    elif "## HEAD（非分支）" in result.stdout:
        return False
    return True


def git_clone(module_name, tag):
    git = builder_modules[module_name]["git"]
    module_dir = f"{basedir}/code/{module_name}"
    if os.path.isdir(module_dir):
        os.chdir(module_dir)
        git_fetch()
        if is_branch():
            git_pull()
        git_checkout(tag)
    else:
        cmd = f"git clone -q -c advice.detachedHead=false -b {tag} {git} {module_dir}"
        result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, check=True)
        os.chdir(module_dir)


def git_pull():
    result = subprocess.run(["git", "pull", "-q"], stdout=subprocess.PIPE, check=True)


def git_fetch():
    result = subprocess.run(["git", "fetch", "-q"], stdout=subprocess.PIPE, check=True)


def log_header(msg):
    print()
    print(f"==> {msg}")


def log_indent(msg):
    if isinstance(msg, bytes):
        msg = msg.decode()
    for line in msg.split("\n"):
        print(f"    {line}")


def log(msg):
    print(msg)


def get_archive_file(module_name, module_type):
    if module_type == "jar":
        module_dict = builder_modules[module_name]
        package_name = module_dict.setdefault('package_name', module_name)
        result = subprocess.run(
            [
                "find",
                "-path",
                "*/target/*",
                "-iname",
                f"{package_name}.{module_type}",
                "-print",
                "-quit",
            ],
            stdout=subprocess.PIPE,
        )
    else:
        result = subprocess.run(
            ["find", "-iname", f"{module_name}.{module_type}", "-print", "-quit"],
            stdout=subprocess.PIPE,
        )
    archive_file = result.stdout.decode().strip()
    if "" == archive_file:
        raise BuildError("[error] archive file not found")
    if "\n" in archive_file:
        raise BuildError(f"[error] multiple archive files found: {archive_file}")
    else:
        return archive_file


def push_to_nexus(module_name, tag):
    upload_file = f"{basedir}/code/{module_name}/{module_name}.zip"
    if push_nexus_enable:
        url = f"https://nexus.luban.fit/repository/luban-raw/docker/plinth/{module_name}/{tag}/{module_name}.zip"
        with open(upload_file, "rb") as fobj:
            res = requests.put(
                url,
                data=fobj,
                auth=(os.environ.get("NEXUS_USER"), os.environ.get("NEXUS_PASSWORD")),
            )
            if res.status_code != 201:
                raise Exception(f"Upload failed for URL: {url}")
            else:
                log_indent(f"upload to nexus {url}")
                return True
    else:
        print("1111")
        os.rename(f"{module_name}.zip", f"{basedir}/others/nginx/pkg/")
        return None


def push_to_harbor(image):
    if push_harbor_enable:
        log_indent(f"push image {image}")
        subprocess.run(f"docker push {image}", shell=True, check=True)
    else:
        log_indent(f"skip push image {image}")


def build_pkg_backend(module_name, tag):
    log_indent(f"tag: {tag}")
    module_dict = builder_modules[module_name]
    cmd = module_dict["build_script"]
    only_sql = module_dict["only_sql"]
    log_indent(f"{cmd}")
    print(1)
    result = subprocess.run(["/bin/bash", "-c", cmd], encoding="utf8", check=True)
    print(2)
    log_indent(f"<returncode [{result.returncode}]>")
    os.chdir(f"{docker_dir}")
    image_name = f"harbor.luban.fit:8384/plinth/{module_name}:{tag}"
    log_indent(f"build image ({image_name})")
    # result = subprocess.run(f'docker buildx build --platform linux/amd64,linux/arm64 --push -q -t {image_name} .',
    result = subprocess.run(
        f"docker buildx build --platform linux/amd64 --push -q -t {image_name} .",
        shell=True,
        check=True,
    )
    push_to_harbor(image_name)


def build_pkg_fe(module_name, tag):
    module_info = builder_modules[module_name]
    cmd = module_info['build_script']
    if 'dist_dir' in module_info:
        #cmd = f"""<<'EOF' bash -ex >>{module_name}.log 2>&1
        cmd = f"""
source ../../node-setenv.sh {module_info['node']}
pkg_path="$(realpath .)/{module_name}.zip"
{cmd}
cd {module_info['dist_dir']}
zip -qr $pkg_path *
"""
    log_indent(f'{cmd}')
    result = subprocess.run(["/bin/bash", "-ec", cmd], encoding="utf8",env={"module_name": module_name, "tag": tag})
    update_fe_config(module_name, tag)


def build_pkg(module_name, tag):
    global has_fe_build
    module_info = builder_modules[module_name]
    if "node" in module_info:
        init_dockerFile(module_name)
        build_pkg_fe(module_name, tag)
        push_to_harbor_fe(module_name, tag)
        archive_file = get_archive_file(module_name, "zip")
        push_to_nexus(module_name, tag)
        has_fe_build = 1
    elif "jdk" in module_info:
        build_pkg_backend(module_name, tag)
    else:
        raise Exception(f"{module_name} not find jdk/node in modules.yml")

def push_to_harbor_fe(module_name, tag):
    module_info = builder_modules[module_name]
    dist = module_info['dist_dir']
    cmd = f"""
    rm -rf docker
    mkdir docker
    cp Dockerfile docker/
    cp -r {dist} docker/{module_name}
    docker build --push -t harbor.luban.fit:8384/plinth/{module_name}:{tag} docker
    """
    log_indent(f'{cmd}')
    result = subprocess.run(["/bin/bash", "-ec", cmd])


class IndentDumper(yaml.Dumper):
    def increase_indent(self, flow=False, indentless=False):
        return super(IndentDumper, self).increase_indent(flow, False)

def init_dockerFile(module_name):
    subprocess.run("pwd")
    cmd = f"""
cat <<EOF > Dockerfile
FROM harbor.luban.fit:8384/library/busybox
WORKDIR /luban/pkg/front-end
COPY {module_name}/ /luban/pkg/front-end/{module_name}/
EOF
"""
    result = subprocess.run(['/bin/bash', '-c', cmd])

def update_compose_file(module_name, newtag, compose_file):
    compose_yaml = yaml.safe_load(open(compose_file))
    image = compose_yaml["services"].get(module_name).get("image")
    if len(image.split(":")) == 1:
        image_name = image
        tag = "latest"
    elif len(image.split(":")) == 2:
        image_name, tag = image.split(":")
    else:
        raise "compose file error: compose_yaml_file"
    if newtag != tag:
        compose_yaml["services"][module_name]["image"] = f"{image_name}:{newtag}"
    yaml.dump(
        compose_yaml,
        open(compose_file, "w"),
        indent=2,
        sort_keys=False,
        Dumper=IndentDumper,
    )


def update_fe_config(module_name, tag):
    fe_dict = {}
    fe_config_file = f"{basedir}/others/nginx/rootfs/luban/ops/fe.txt"
    with open(fe_config_file) as fobj:
        for line in fobj:
            line = line.strip()
            m, t = line.split(":", 1)
            fe_dict[m] = t
    fe_dict[module_name] = tag
    with open(fe_config_file, "w") as fobj:
        for k, v in sorted(fe_dict.items()):
            fobj.write(f"{k}:{v}\n")

def artifact_is_exist(module_name, tag):
    module_info = builder_modules[module_name]
    if "node" in module_info:
        artifact_url = f"https://nexus.luban.fit/repository/luban-raw/docker/plinth/{module_name}/{tag}/{module_name}.zip"
        res = requests.head(artifact_url)
        if res.status_code == 200:
            print(f'artifact is exist "{artifact_url}"')
            return True
    if "jdk" in module_info:
        artifact_url = f"https://harbor.luban.fit:8384/api/v2.0/projects/plinth/repositories/{module_name}/artifacts/{tag}"
        res = requests.get(artifact_url)
        if res.status_code == 200:
            print(f'artifact is exist "{artifact_url}"')
            return True
    #artifact_url = f"https://harbor.luban.fit:8384/api/v2.0/projects/plinth/repositories/{module_name}/artifacts/{tag}"
    #res = requests.get(artifact_url)
    #if res.status_code == 200:
    #    print(f'artifact is exist "{artifact_url}"')
    #    return True
    return False

@app.get("/pack")
async def pack(app: str = '', tag: str = ''):
    try:
        if artifact_is_exist(app, tag):
            return {app + tag + " 包已存在"}
        git_clone(app, tag)
        print('代码拉取成功...')
        build_pkg(app, tag)
        print('打包成功...')
    except GitCheckoutError as e:
        return e
    except FeBuildError as e:
        return e
    except :
        return '未知错误'
    return f"harbor.luban.fit:8384/plinth/{app}:{tag}"