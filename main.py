#!/usr/bin/env python3
# encoding: utf-8
"""
Luban DevOps - 自动编译打包平台
重构版本 - 模块化架构

作者: Claude 4.0 sonnet
"""

import uvicorn
from fastapi import FastAPI
from api.routes import router
from utils.logger import setup_logger
from config.settings import settings

# 初始化日志系统
logger = setup_logger()

def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    app = FastAPI(
        title="Luban DevOps Platform",
        description="自动编译打包平台 - 重构版",
        version="2.0.0"
    )

    # 注册路由
    app.include_router(router, prefix="/api/v1")

    return app

app = create_app()

if __name__ == '__main__':
    logger.info("启动 Luban DevOps Platform...")
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
