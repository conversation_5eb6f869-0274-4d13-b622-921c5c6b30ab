#!/usr/bin/env python3
# encoding: utf-8
"""
Git操作管理器
封装所有Git相关操作，提供统一的接口
"""

import os
import subprocess
from pathlib import Path
from typing import Optional, Tuple
from utils.exceptions import GitError, GitCheckoutError
from utils.logger import get_logger

logger = get_logger(__name__)


class GitManager:
    """Git操作管理器"""
    
    def __init__(self, work_dir: str):
        """
        初始化Git管理器
        
        Args:
            work_dir: 工作目录
        """
        self.work_dir = Path(work_dir)
        self.work_dir.mkdir(parents=True, exist_ok=True)
    
    def clone_or_update(self, repo_url: str, module_name: str, tag: str) -> str:
        """
        克隆或更新代码仓库
        
        Args:
            repo_url: 仓库URL
            module_name: 模块名称
            tag: 标签或分支名
            
        Returns:
            模块目录路径
        """
        module_dir = self.work_dir / module_name
        
        try:
            if module_dir.exists():
                logger.info(f"更新现有仓库: {module_name}")
                self._update_existing_repo(module_dir, tag)
            else:
                logger.info(f"克隆新仓库: {module_name}")
                self._clone_new_repo(repo_url, module_dir, tag)
            
            return str(module_dir)
            
        except Exception as e:
            logger.error(f"Git操作失败 {module_name}: {e}")
            raise GitError(f"Git操作失败: {e}")
    
    def _clone_new_repo(self, repo_url: str, target_dir: Path, tag: str) -> None:
        """
        克隆新仓库
        
        Args:
            repo_url: 仓库URL
            target_dir: 目标目录
            tag: 标签或分支名
        """
        cmd = [
            "git", "clone", "-q",
            "-c", "advice.detachedHead=false",
            "-b", tag,
            repo_url,
            str(target_dir)
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=self.work_dir
        )
        
        if result.returncode != 0:
            raise GitError(f"克隆仓库失败: {result.stderr}")
        
        logger.info(f"成功克隆仓库到: {target_dir}")
    
    def _update_existing_repo(self, repo_dir: Path, tag: str) -> None:
        """
        更新现有仓库
        
        Args:
            repo_dir: 仓库目录
            tag: 标签或分支名
        """
        # 切换到仓库目录
        original_cwd = os.getcwd()
        try:
            os.chdir(repo_dir)
            
            # 获取远程更新
            self._git_fetch()
            
            # 如果当前在分支上，先拉取最新代码
            if self._is_on_branch():
                self._git_pull()
            
            # 切换到指定标签或分支
            self._git_checkout(tag)
            
        finally:
            os.chdir(original_cwd)
    
    def _git_fetch(self) -> None:
        """执行git fetch"""
        result = subprocess.run(
            ["git", "fetch", "-q"],
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            raise GitError(f"git fetch失败: {result.stderr}")
    
    def _git_pull(self) -> None:
        """执行git pull"""
        result = subprocess.run(
            ["git", "pull", "-q"],
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            raise GitError(f"git pull失败: {result.stderr}")
    
    def _git_checkout(self, tag: str) -> None:
        """
        切换到指定标签或分支
        
        Args:
            tag: 标签或分支名
        """
        result = subprocess.run(
            ["git", "checkout", tag],
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            raise GitCheckoutError(f"切换到 {tag} 失败: {result.stderr}")
        
        logger.info(f"成功切换到: {tag}")
    
    def _is_on_branch(self) -> bool:
        """
        检查当前是否在分支上（而不是detached HEAD状态）
        
        Returns:
            True if on branch, False if detached HEAD
        """
        result = subprocess.run(
            ["git", "status", "-s", "-b"],
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            return False
        
        status_output = result.stdout
        # 检查是否为detached HEAD状态
        return not ("## HEAD (no branch)" in status_output or "## HEAD（非分支）" in status_output)
    
    def get_current_tag(self) -> str:
        """
        获取当前HEAD指向的标签
        
        Returns:
            标签名，如果没有标签则返回'latest'
        """
        result = subprocess.run(
            ["git", "tag", "--points-at=HEAD"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0 and result.stdout.strip():
            tag = result.stdout.strip().split('\n')[0]
            return tag
        
        return "latest"
    
    def get_current_commit(self) -> str:
        """
        获取当前commit hash
        
        Returns:
            commit hash
        """
        result = subprocess.run(
            ["git", "rev-parse", "HEAD"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            return result.stdout.strip()[:8]  # 返回短hash
        
        return "unknown"
    
    def get_repo_info(self) -> Tuple[str, str]:
        """
        获取仓库信息
        
        Returns:
            (当前标签/分支, commit hash)
        """
        return self.get_current_tag(), self.get_current_commit()
