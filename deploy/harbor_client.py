#!/usr/bin/env python3
# encoding: utf-8
"""
Harbor镜像仓库客户端
处理Docker镜像的构建和推送
"""

import os
import subprocess
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any
from config.settings import settings
from utils.exceptions import HarborError
from utils.logger import get_logger

logger = get_logger(__name__)


class HarborClient:
    """Harbor镜像仓库客户端"""
    
    def __init__(self):
        self.registry_url = settings.harbor_registry_url
        self.username = settings.HARBOR_USERNAME
        self.password = settings.HARBOR_PASSWORD
        self.push_enabled = settings.PUSH_HARBOR_ENABLE
    
    def build_and_push_image(
        self,
        module_name: str,
        tag: str,
        context_dir: str,
        dockerfile_content: Optional[str] = None,
        dockerfile_path: Optional[str] = None
    ) -> str:
        """
        构建并推送Docker镜像
        
        Args:
            module_name: 模块名称
            tag: 镜像标签
            context_dir: 构建上下文目录
            dockerfile_content: Dockerfile内容（可选）
            dockerfile_path: Dockerfile路径（可选）
            
        Returns:
            完整的镜像名称
        """
        image_name = f"{self.registry_url}/{module_name}:{tag}"
        
        try:
            # 准备Dockerfile
            if dockerfile_content:
                dockerfile_path = self._create_temp_dockerfile(dockerfile_content)
            elif not dockerfile_path:
                dockerfile_path = os.path.join(context_dir, "Dockerfile")
            
            # 构建镜像
            self._build_image(image_name, context_dir, dockerfile_path)
            
            # 推送镜像
            if self.push_enabled:
                self._push_image(image_name)
            else:
                logger.info(f"跳过推送镜像: {image_name}")
            
            return image_name
            
        except Exception as e:
            logger.error(f"构建或推送镜像失败 {module_name}: {e}")
            raise HarborError(f"镜像操作失败: {e}")
    
    def _build_image(self, image_name: str, context_dir: str, dockerfile_path: str) -> None:
        """
        构建Docker镜像
        
        Args:
            image_name: 镜像名称
            context_dir: 构建上下文目录
            dockerfile_path: Dockerfile路径
        """
        logger.info(f"构建镜像: {image_name}")
        
        cmd = [
            "docker", "buildx", "build",
            "--platform", "linux/amd64",
            "-t", image_name,
            "-f", dockerfile_path,
            context_dir
        ]
        
        if self.push_enabled:
            cmd.insert(-1, "--push")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=1800  # 30分钟超时
        )
        
        if result.returncode != 0:
            raise HarborError(f"构建镜像失败: {result.stderr}")
        
        logger.info(f"镜像构建成功: {image_name}")
    
    def _push_image(self, image_name: str) -> None:
        """
        推送Docker镜像
        
        Args:
            image_name: 镜像名称
        """
        if not self.push_enabled:
            return
        
        logger.info(f"推送镜像: {image_name}")
        
        # 登录Harbor（如果提供了认证信息）
        if self.username and self.password:
            self._login()
        
        result = subprocess.run(
            ["docker", "push", image_name],
            capture_output=True,
            text=True,
            timeout=1800  # 30分钟超时
        )
        
        if result.returncode != 0:
            raise HarborError(f"推送镜像失败: {result.stderr}")
        
        logger.info(f"镜像推送成功: {image_name}")
    
    def _login(self) -> None:
        """登录Harbor"""
        if not self.username or not self.password:
            logger.warning("Harbor认证信息未配置，跳过登录")
            return
        
        result = subprocess.run(
            ["docker", "login", settings.HARBOR_HOST, "-u", self.username, "-p", self.password],
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            raise HarborError(f"Harbor登录失败: {result.stderr}")
        
        logger.info("Harbor登录成功")
    
    def _create_temp_dockerfile(self, content: str) -> str:
        """
        创建临时Dockerfile
        
        Args:
            content: Dockerfile内容
            
        Returns:
            临时文件路径
        """
        with tempfile.NamedTemporaryFile(mode='w', suffix='.Dockerfile', delete=False) as f:
            f.write(content)
            return f.name
    
    def create_java_dockerfile(self, module_name: str, jar_path: str) -> str:
        """
        创建Java应用的Dockerfile
        
        Args:
            module_name: 模块名称
            jar_path: JAR文件路径
            
        Returns:
            Dockerfile内容
        """
        jar_name = Path(jar_path).name
        
        dockerfile_content = f"""
FROM harbor.luban.fit:8384/library/openjdk:17-jre-slim

WORKDIR /app

# 复制JAR文件
COPY {jar_name} /app/app.jar

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \\
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app/app.jar"]
"""
        return dockerfile_content.strip()
    
    def create_frontend_dockerfile(self, module_name: str, dist_dir: str) -> str:
        """
        创建前端应用的Dockerfile
        
        Args:
            module_name: 模块名称
            dist_dir: 构建输出目录
            
        Returns:
            Dockerfile内容
        """
        dockerfile_content = f"""
FROM harbor.luban.fit:8384/library/nginx:alpine

# 复制构建产物
COPY {dist_dir}/ /usr/share/nginx/html/{module_name}/

# 复制nginx配置（如果存在）
COPY nginx.conf /etc/nginx/nginx.conf 2>/dev/null || true

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
"""
        return dockerfile_content.strip()
    
    def check_image_exists(self, module_name: str, tag: str) -> bool:
        """
        检查镜像是否存在
        
        Args:
            module_name: 模块名称
            tag: 镜像标签
            
        Returns:
            镜像是否存在
        """
        image_name = f"{self.registry_url}/{module_name}:{tag}"
        
        result = subprocess.run(
            ["docker", "manifest", "inspect", image_name],
            capture_output=True,
            text=True
        )
        
        return result.returncode == 0
    
    def get_image_info(self, module_name: str, tag: str) -> Dict[str, Any]:
        """
        获取镜像信息
        
        Args:
            module_name: 模块名称
            tag: 镜像标签
            
        Returns:
            镜像信息字典
        """
        return {
            'module_name': module_name,
            'tag': tag,
            'image_name': f"{self.registry_url}/{module_name}:{tag}",
            'registry_url': self.registry_url,
            'push_enabled': self.push_enabled
        }
