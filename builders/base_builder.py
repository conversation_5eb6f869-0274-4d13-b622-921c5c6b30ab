#!/usr/bin/env python3
# encoding: utf-8
"""
构建器基类
定义所有构建器的通用接口和行为
"""

import os
import subprocess
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any, Optional
from config.module_config import ModuleConfig
from utils.exceptions import BuildError
from utils.logger import get_logger

logger = get_logger(__name__)


class BaseBuilder(ABC):
    """构建器基类"""
    
    def __init__(self, module_config: ModuleConfig, work_dir: str):
        """
        初始化构建器
        
        Args:
            module_config: 模块配置
            work_dir: 工作目录
        """
        self.config = module_config
        self.work_dir = Path(work_dir)
        self.module_dir = self.work_dir / module_config.name
        self.build_env = self._prepare_build_environment()
    
    @abstractmethod
    def build(self, tag: str) -> str:
        """
        执行构建
        
        Args:
            tag: 构建标签
            
        Returns:
            构建产物路径
        """
        pass
    
    @abstractmethod
    def get_artifact_path(self) -> str:
        """
        获取构建产物路径
        
        Returns:
            构建产物路径
        """
        pass
    
    def _prepare_build_environment(self) -> Dict[str, str]:
        """
        准备构建环境变量
        
        Returns:
            环境变量字典
        """
        env = os.environ.copy()
        env.update({
            'MODULE_NAME': self.config.name,
            'BUILD_DIR': str(self.module_dir)
        })
        return env
    
    def _execute_build_script(self, script: str, tag: str) -> subprocess.CompletedProcess:
        """
        执行构建脚本
        
        Args:
            script: 构建脚本
            tag: 构建标签
            
        Returns:
            执行结果
        """
        if not script.strip():
            raise BuildError(f"模块 {self.config.name} 的构建脚本为空")
        
        logger.info(f"开始执行构建脚本: {self.config.name}")
        logger.debug(f"构建脚本内容:\n{script}")
        
        # 切换到模块目录
        original_cwd = os.getcwd()
        try:
            os.chdir(self.module_dir)
            
            # 更新环境变量
            build_env = self.build_env.copy()
            build_env['TAG'] = tag
            
            # 执行构建脚本
            result = subprocess.run(
                ["/bin/bash", "-ec", script],
                env=build_env,
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            
            if result.returncode != 0:
                logger.error(f"构建失败 {self.config.name}:")
                logger.error(f"stdout: {result.stdout}")
                logger.error(f"stderr: {result.stderr}")
                raise BuildError(f"构建脚本执行失败: {result.stderr}")
            
            logger.info(f"构建成功: {self.config.name}")
            if result.stdout:
                logger.debug(f"构建输出: {result.stdout}")
            
            return result
            
        except subprocess.TimeoutExpired:
            raise BuildError(f"构建超时: {self.config.name}")
        except Exception as e:
            raise BuildError(f"构建过程中发生错误: {e}")
        finally:
            os.chdir(original_cwd)
    
    def _validate_module_directory(self) -> None:
        """验证模块目录是否存在"""
        if not self.module_dir.exists():
            raise BuildError(f"模块目录不存在: {self.module_dir}")
        
        if not self.module_dir.is_dir():
            raise BuildError(f"模块路径不是目录: {self.module_dir}")
    
    def pre_build_hook(self, tag: str) -> None:
        """
        构建前钩子方法
        子类可以重写此方法来执行构建前的准备工作
        
        Args:
            tag: 构建标签
        """
        self._validate_module_directory()
        logger.info(f"开始构建模块: {self.config.name} (tag: {tag})")
    
    def post_build_hook(self, tag: str, artifact_path: str) -> None:
        """
        构建后钩子方法
        子类可以重写此方法来执行构建后的清理工作
        
        Args:
            tag: 构建标签
            artifact_path: 构建产物路径
        """
        logger.info(f"模块构建完成: {self.config.name}")
        logger.info(f"构建产物: {artifact_path}")
    
    def get_build_info(self) -> Dict[str, Any]:
        """
        获取构建信息
        
        Returns:
            构建信息字典
        """
        return {
            'module_name': self.config.name,
            'project_type': self.config.project_type,
            'git_url': self.config.git,
            'work_dir': str(self.work_dir),
            'module_dir': str(self.module_dir)
        }
