# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=false

# 基础路径配置
BASE_DIR=/luban/work/plinth
CODE_DIR=/luban/work/plinth/code
NGINX_PKG_DIR=/luban/work/plinth/others/nginx/pkg/
DOCKER_TEMPLATE_DIR=/luban/work/plinth/frontend
COMPOSE_FILE=/luban/work/plinth-compose/docker-compose.yml
MODULE_CONFIG_FILE=/luban/work/plinth/module.yml

# Harbor配置
HARBOR_HOST=harbor.luban.fit:8384
HARBOR_PROJECT=plinth
HARBOR_USERNAME=
HARBOR_PASSWORD=

# Nexus配置
NEXUS_HOST=nexus.luban.fit
NEXUS_REPOSITORY=luban-raw
NEXUS_USER=
NEXUS_PASSWORD=

# 功能开关
PUSH_HARBOR_ENABLE=true
PUSH_NEXUS_ENABLE=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/devops.log
